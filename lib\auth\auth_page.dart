import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:upshift/auth/firebase_ui_auth_screen.dart';
import 'package:upshift/chat/upshift_chat.dart';

class AuthPage extends StatelessWidget {
  const AuthPage({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // If user is logged in
        if (snapshot.hasData) {
          return const UpshiftHomePage();
        }
        // If user is NOT logged in
        else {
          return const FirebaseUIAuthScreen();
        }
      },
    );
  }
}
