import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import '../gemini.dart';

class UpshiftHomePage extends StatefulWidget {
  const UpshiftHomePage({super.key});

  @override
  State<UpshiftHomePage> createState() => _UpshiftHomePageState();
}

class _UpshiftHomePageState extends State<UpshiftHomePage> {
  final _dio = Dio();

  final _chatIdController = TextEditingController(
    text: dotenv.env['DEFAULT_CHAT_ID'] ?? '',
  );
  final _geminiApiKeyController = TextEditingController(
    text: dotenv.env['GEMINI_API_KEY'] ?? '',
  );

  UserID _currentUserId = 'john'; // TODO get from firebase

  @override
  void dispose() {
    _geminiApiKeyController.dispose();
    _chatIdController.dispose();
    _dio.close(force: true);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 8),
              SizedBox(
                width: 400,
                child: TextField(
                  controller: _chatIdController,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: 'Сhat id',
                  ),
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) =>
                          Gemini(geminiApiKey: _geminiApiKeyController.text),
                    ),
                  );
                },
                child: const Text('gemini'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
