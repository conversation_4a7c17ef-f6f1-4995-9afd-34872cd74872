import 'package:json_annotation/json_annotation.dart';

part 'models.g.dart'; // <- generated code

@JsonSerializable()
class User {
  final String id;
  final String name;
  final String email;
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.createdAt,
  });

  // From JSON factory
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserToJson(this);
}

@JsonSerializable()
class Participant {
  final String personaId;
  final String name;
  final String avatarUrl;

  Participant({
    required this.personaId,
    required this.name,
    required this.avatarUrl,
  });

  // From JSON factory
  factory Participant.fromJson(Map<String, dynamic> json) =>
      _$ParticipantFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ParticipantToJson(this);
}

@JsonSerializable()
class Chat {
  final String title;
  final String ownerId;
  final DateTime startedDate;
  final DateTime lastUpdatedDate;
  final bool isCompleted;
  final List<Participant> participants;
  final bool? archived;
  final Map<String, dynamic>? metadata;

  Chat({
    required this.title,
    required this.ownerId,
    required this.startedDate,
    required this.lastUpdatedDate,
    required this.isCompleted,
    required this.participants,
    this.archived,
    this.metadata,
  });

  // From JSON factory
  factory Chat.fromJson(Map<String, dynamic> json) => _$ChatFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ChatToJson(this);
}
