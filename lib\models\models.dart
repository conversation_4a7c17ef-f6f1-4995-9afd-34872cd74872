import 'package:json_annotation/json_annotation.dart';

part 'models.g.dart'; // <- generated code

@JsonSerializable()
class User {
  final String id;
  final String name;
  final String email;
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.createdAt,
  });

  // From JSON factory
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
