import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

part 'models.g.dart'; // <- generated code

@JsonSerializable()
class User {
  final String id;
  final String name;
  final String email;
  final Timestamp createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.createdAt,
  });

  // From JSON factory
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserToJson(this);
}

@JsonSerializable()
class ChatParticipant {
  final String personaId;
  final String name;
  final String avatarUrl;

  ChatParticipant({
    required this.personaId,
    required this.name,
    required this.avatarUrl,
  });

  // From JSON factory
  factory ChatParticipant.fromJson(Map<String, dynamic> json) =>
      _$ChatParticipantFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ChatParticipantToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Chat {
  final String title;
  final String ownerId;
  final Timestamp startedDate;
  final Timestamp lastUpdatedDate;
  final bool isCompleted;
  final List<ChatParticipant> participants;
  final bool? archived;
  final Map<String, dynamic>? metadata;

  Chat({
    required this.title,
    required this.ownerId,
    required this.startedDate,
    required this.lastUpdatedDate,
    required this.isCompleted,
    required this.participants,
    this.archived,
    this.metadata,
  });

  // From JSON factory
  factory Chat.fromJson(Map<String, dynamic> json) => _$ChatFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ChatToJson(this);
}
