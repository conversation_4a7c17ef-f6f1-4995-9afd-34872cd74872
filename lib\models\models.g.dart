// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$User<PERSON>rom<PERSON>son(Map<String, dynamic> json) => User(
  id: json['id'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'email': instance.email,
  'createdAt': instance.createdAt.toIso8601String(),
};

Participant _$ParticipantFromJson(Map<String, dynamic> json) => Participant(
  personaId: json['personaId'] as String,
  name: json['name'] as String,
  avatarUrl: json['avatarUrl'] as String,
);

Map<String, dynamic> _$ParticipantToJson(Participant instance) =>
    <String, dynamic>{
      'personaId': instance.personaId,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
    };

Chat _$ChatFromJson(Map<String, dynamic> json) => Chat(
  title: json['title'] as String,
  ownerId: json['ownerId'] as String,
  startedDate: DateTime.parse(json['startedDate'] as String),
  lastUpdatedDate: DateTime.parse(json['lastUpdatedDate'] as String),
  isCompleted: json['isCompleted'] as bool,
  participants: (json['participants'] as List<dynamic>)
      .map((e) => Participant.fromJson(e as Map<String, dynamic>))
      .toList(),
  archived: json['archived'] as bool?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ChatToJson(Chat instance) => <String, dynamic>{
  'title': instance.title,
  'ownerId': instance.ownerId,
  'startedDate': instance.startedDate.toIso8601String(),
  'lastUpdatedDate': instance.lastUpdatedDate.toIso8601String(),
  'isCompleted': instance.isCompleted,
  'participants': instance.participants,
  'archived': instance.archived,
  'metadata': instance.metadata,
};
