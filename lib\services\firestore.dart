import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/models.dart';

class FirestoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get user by userId
  static Future<User?> getUser(String userId) async {
    try {
      final docSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        return User.fromJson(docSnapshot.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  // Get all chats by ownerId (user uuid)
  static Future<List<Chat>> getChats(String ownerId) async {
    try {
      final querySnapshot = await _firestore
          .collection('chats')
          .where('ownerId', isEqualTo: ownerId)
          .orderBy('lastUpdatedDate', descending: true)
          .get();

      return querySnapshot.docs.map((doc) => _chatFromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get chats: $e');
    }
  }

  // Get specific chat by ownerId and chatId
  static Future<Chat?> getChat(String ownerId, String chatId) async {
    try {
      final docSnapshot = await _firestore
          .collection('chats')
          .doc(chatId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final chat = _chatFromFirestore(docSnapshot);

        // Verify that the chat belongs to the specified owner
        if (chat.ownerId == ownerId) {
          return chat;
        } else {
          throw Exception('Chat does not belong to the specified owner');
        }
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get chat: $e');
    }
  }

  // Helper method to convert Firestore document to Chat object
  static Chat _chatFromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Convert Firestore Timestamps to DateTime, then to ISO string for JSON parsing
    final startedDate = (data['startedDate'] as Timestamp).toDate();
    final lastUpdatedDate = (data['lastUpdatedDate'] as Timestamp).toDate();

    // Create a new map with converted timestamps
    final convertedData = Map<String, dynamic>.from(data);
    convertedData['id'] = doc.id; // Add the document ID
    convertedData['startedDate'] = startedDate.toIso8601String();
    convertedData['lastUpdatedDate'] = lastUpdatedDate.toIso8601String();

    return Chat.fromJson(convertedData);
  }
}
